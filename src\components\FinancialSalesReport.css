/* أنماط تقرير المبيعات والمالية المدمج */
.financial-sales-report {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
}

/* حالة فارغة */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
  color: #718096;
  text-align: center;
}

.empty-state.small {
  padding: 30px 20px;
}

.empty-state-hint {
  font-size: 0.9rem;
  color: #a0aec0;
  margin-top: 10px;
}

.empty-state svg {
  color: #1a3a5f;
  margin-bottom: 20px;
  opacity: 0.7;
}

.empty-state p {
  font-size: 1.2rem;
  font-weight: 500;
}

/* شريط التبويبات */
.financial-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 25px;
  background-color: white;
  padding: 10px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.financial-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background-color: transparent;
  color: #2d3748;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.financial-tab:hover {
  background-color: rgba(26, 58, 95, 0.05);
  color: #1a3a5f;
}

.financial-tab.active {
  background-color: #1a3a5f;
  color: white;
}

.financial-tab svg {
  font-size: 1.2rem;
}

/* فلتر النطاق الزمني */
.date-range-filter {
  margin-bottom: 25px;
  display: flex;
  justify-content: flex-end;
}

.date-range-filter label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #2d3748;
}

.date-range-filter select {
  padding: 8px 15px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background-color: white;
  font-size: 0.95rem;
  transition: border-color 0.3s ease;
}

.date-range-filter select:focus {
  border-color: #1a3a5f;
  outline: none;
  box-shadow: 0 0 0 2px rgba(26, 58, 95, 0.2);
}

/* محتوى التبويب */
.financial-content {
  background-color: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* عنوان القسم */
.section-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1a3a5f;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e2e8f0;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -1px;
  right: 0;
  width: 100px;
  height: 3px;
  background: linear-gradient(to right, #1a3a5f, #3498db);
  border-radius: 1.5px;
}

.section-subtitle {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.stat-card {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stat-card-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  margin-bottom: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.stat-card-icon::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transform: scale(0.5);
  transition: transform 0.5s, opacity 0.5s;
}

.stat-card:hover .stat-card-icon::after {
  opacity: 0.3;
  transform: scale(1);
}

.stat-card-title {
  font-weight: 700;
  color: #1a3a5f;
  margin-bottom: 5px;
  font-size: 1.1rem;
  text-shadow: 0.5px 0.5px 1px rgba(0, 0, 0, 0.05);
  position: relative;
  padding-bottom: 8px;
}

.stat-card-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background-color: currentColor;
  opacity: 0.5;
}

.stat-card-subtitle {
  color: #4a5568;
  font-size: 0.95rem;
  font-weight: 500;
  margin-top: 5px;
  padding: 3px 8px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.03);
  display: inline-block;
}

/* تخصيص بطاقات الإحصائيات */
.stat-card.sales-card .stat-card-icon {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.stat-card.profit-card .stat-card-icon {
  background-color: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.stat-card.purchases-card .stat-card-icon {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.stat-card.cashbox-card .stat-card-icon {
  background-color: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.cashbox-initial-card .stat-card-icon {
  background-color: rgba(26, 58, 95, 0.1);
  color: #1a3a5f;
}

.cashbox-income-card .stat-card-icon {
  background-color: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.cashbox-expense-card .stat-card-icon {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.cashbox-current-card .stat-card-icon {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

/* تخصيص بطاقات الأرباح */
.stat-card.profit-quarterly {
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  border-right: 4px solid #3498db;
  border-bottom: 1px solid #e2e8f0;
}

.stat-card.profit-half-yearly {
  background: linear-gradient(135deg, #ffffff 0%, #f0fff4 100%);
  border-right: 4px solid #27ae60;
  border-bottom: 1px solid #e2e8f0;
}

.stat-card.profit-three-quarters {
  background: linear-gradient(135deg, #ffffff 0%, #fff8f0 100%);
  border-right: 4px solid #e67e22;
  border-bottom: 1px solid #e2e8f0;
}

.stat-card.profit-yearly {
  background: linear-gradient(135deg, #ffffff 0%, #f5f0ff 100%);
  border-right: 4px solid #9b59b6;
  border-bottom: 1px solid #e2e8f0;
}

/* أقسام لوحة المعلومات */
.dashboard-section {
  margin-bottom: 40px;
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
}

/* تحليل المبيعات والأرباح */
.analysis-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.month-filter {
  display: flex;
  align-items: center;
  gap: 10px;
}

.month-select {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background-color: #fff;
  font-family: 'Cairo', sans-serif;
  font-size: 14px;
  min-width: 180px;
  direction: rtl;
}

.month-select option {
  font-family: 'Cairo', sans-serif;
  padding: 5px;
}

.refresh-button {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-button:hover {
  background-color: #e9ecef;
}

.cashbox-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.analysis-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  height: 400px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.analysis-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.analysis-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e2e8f0;
}

.analysis-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 8px;
}

.analysis-card-content {
  height: calc(100% - 50px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container {
  width: 100%;
  height: 100%;
  direction: ltr;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #718096;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 30px;
  width: 100%;
  height: 100%;
  text-align: center;
}

.chart-placeholder svg {
  color: #1a3a5f;
  margin-bottom: 15px;
  opacity: 0.7;
}

.analysis-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-right: 4px solid #1a3a5f;
}

.analysis-summary p {
  margin-bottom: 10px;
  font-size: 1.05rem;
}

/* توزيع الأرباح */
.profit-distribution {
  display: flex;
  flex-direction: row;
  gap: 15px;
  margin-bottom: 30px;
  width: 100%;
  min-height: 150px;
}

.profit-distribution-item {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 10px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.profit-distribution-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
}

.profit-distribution-item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  opacity: 0.5;
}

.investment-item::before {
  background: linear-gradient(135deg, rgba(41, 128, 185, 0.1) 0%, rgba(41, 128, 185, 0.2) 100%);
}

.profit-item::before {
  background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(39, 174, 96, 0.2) 100%);
}

.investment-item {
  border: 2px solid #3498db;
  background-color: #f0f9ff;
}

.profit-item {
  border: 2px solid #27ae60;
  background-color: #f0fff4;
}

.investment-item .profit-distribution-value {
  color: #2980b9;
  background-color: white;
  border: 1px solid rgba(41, 128, 185, 0.2);
  box-shadow: 0 4px 8px rgba(41, 128, 185, 0.1);
}

.profit-item .profit-distribution-value {
  color: #27ae60;
  background-color: white;
  border: 1px solid rgba(39, 174, 96, 0.2);
  box-shadow: 0 4px 8px rgba(39, 174, 96, 0.1);
}

.profit-distribution-label {
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
  font-size: 1.2rem;
  text-align: center;
  padding: 5px 15px;
  border-radius: 20px;
  background-color: white;
  display: inline-block;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.investment-item .profit-distribution-label {
  color: #2980b9;
  border-bottom: 2px solid #3498db;
}

.profit-item .profit-distribution-label {
  color: #27ae60;
  border-bottom: 2px solid #27ae60;
}

.profit-distribution-value {
  font-size: 1.8rem;
  font-weight: 800;
  position: relative;
  z-index: 1;
  text-align: center;
  padding: 12px 20px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-top: 10px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.investment-value {
  color: #2980b9 !important;
}

.profit-value {
  color: #27ae60 !important;
}

.profit-distribution-item:hover .profit-distribution-value {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .financial-sales-report {
    padding: 15px;
  }

  .financial-tabs {
    flex-direction: column;
    gap: 5px;
  }

  .financial-tab {
    width: 100%;
    justify-content: flex-start;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .analysis-grid {
    grid-template-columns: 1fr;
  }

  .analysis-card {
    height: 350px;
  }
}

/* تأثيرات متحركة */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.stat-card-value {
  animation: pulse 3s infinite;
  font-size: 2rem;
  font-weight: 800;
  color: #000000;
  margin: 10px 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  display: inline-block;
}

/* تخصيص قيم الأرباح في البطاقات */
.profit-quarterly .stat-card-value {
  color: #3498db;
}

.profit-half-yearly .stat-card-value {
  color: #27ae60;
}

.profit-three-quarters .stat-card-value {
  color: #e67e22;
}

.profit-yearly .stat-card-value {
  color: #9b59b6;
}

/* تأثير متحرك لقيم الأرباح */
@keyframes highlight {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.financial-profits .stat-card-value {
  background: linear-gradient(90deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.8) 50%,
    rgba(255,255,255,0) 100%);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: highlight 3s ease-in-out infinite;
  font-size: 2.2rem;
  font-weight: 900;
  letter-spacing: 0.5px;
}

/* تخصيص أيقونات بطاقات الأرباح */
.profit-quarterly .stat-card-icon {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.profit-half-yearly .stat-card-icon {
  background-color: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.profit-three-quarters .stat-card-icon {
  background-color: rgba(230, 126, 34, 0.1);
  color: #e67e22;
}

.profit-yearly .stat-card-icon {
  background-color: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

/* تنسيق الجداول */
.financial-sales-report .app-datatable-table th {
  background-color: rgba(26, 58, 95, 0.05);
  color: #1a3a5f;
  font-weight: 600;
}

.financial-sales-report .app-datatable-table tr:hover {
  background-color: rgba(26, 58, 95, 0.03);
}

.text-success {
  color: #27ae60 !important;
}

.text-danger {
  color: #e74c3c !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

/* تنسيقات خاصة بتبويب المشتريات */
.financial-purchases .dashboard-section {
  border-right: 4px solid #28a745;
}

.financial-purchases .section-subtitle {
  color: #28a745;
}

.month-filter-container {
  margin-bottom: 15px;
  text-align: left;
  display: flex;
  justify-content: flex-end;
}

.month-filter-container .month-filter {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f8f9fa;
  font-size: 14px;
  min-width: 200px;
  font-family: 'Cairo', sans-serif;
}

.month-filter-container .month-filter option {
  font-family: 'Cairo', sans-serif;
  padding: 5px;
}

.financial-purchases .stat-card {
  border-right: 4px solid #28a745;
}

.financial-purchases .stat-card-icon {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

/* تنسيقات تحليل الأرباح الشهرية */
.profit-analysis-section {
  background-color: #f8f9fa;
  border-right: 4px solid #27ae60;
}

.profit-analysis-section .section-subtitle {
  color: #27ae60;
}

.monthly-data-cell {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 5px;
  text-align: center;
  font-weight: 500;
}

.profit-cell {
  background-color: rgba(39, 174, 96, 0.1);
}

.profit-percentage-cell {
  background-color: rgba(26, 58, 95, 0.1);
}

/* تنسيقات فلتر الشهر */
.month-filter-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.month-filter {
  padding: 8px 15px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background-color: white;
  font-size: 0.95rem;
  transition: border-color 0.3s ease;
  min-width: 200px;
}

.month-filter:focus {
  border-color: #28a745;
  outline: none;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}
