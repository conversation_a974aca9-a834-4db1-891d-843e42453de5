/**
 * وحدة حساب الربح
 * تقوم بحساب الربح بناءً على سعر البيع وسعر الشراء والكمية
 */

/**
 * حساب الربح
 * @param {number} sellingPrice - سعر البيع
 * @param {number} costPrice - سعر التكلفة
 * @param {number} quantity - الكمية
 * @returns {number} - الربح
 */
function calculateProfit(sellingPrice, costPrice, quantity) {
  try {
    // التحويل إلى أرقام
    const numericSellingPrice = Number(sellingPrice);
    const numericCostPrice = Number(costPrice);
    const numericQuantity = Number(quantity);

    // التحقق من صحة البيانات
    if (isNaN(numericSellingPrice) || numericSellingPrice < 0) {
      console.warn(`سعر البيع غير صالح: ${sellingPrice}`);
      return 0;
    }

    if (isNaN(numericCostPrice) || numericCostPrice < 0) {
      console.warn(`سعر التكلفة غير صالح: ${costPrice}`);
      return 0;
    }

    if (isNaN(numericQuantity) || numericQuantity <= 0) {
      console.warn(`الكمية غير صالحة: ${quantity}`);
      return 0;
    }

    // حساب الربح
    const profit = (numericSellingPrice - numericCostPrice) * numericQuantity;

    // تسجيل معلومات للتشخيص
    console.log(`[PROFIT-FIX] حساب الربح: (${numericSellingPrice} - ${numericCostPrice}) × ${numericQuantity} = ${profit}`);

    // التأكد من أن الربح لا يكون سالباً (في حالة كان سعر البيع أقل من سعر الشراء)
    const finalProfit = Math.max(0, profit);

    // تقريب الربح إلى رقمين عشريين
    return Math.round(finalProfit * 100) / 100;
  } catch (error) {
    console.error('خطأ في حساب الربح:', error);
    return 0;
  }
}

/**
 * حساب نسبة الربح
 * @param {number} sellingPrice - سعر البيع
 * @param {number} costPrice - سعر التكلفة
 * @returns {number} - نسبة الربح
 */
function calculateProfitMargin(sellingPrice, costPrice) {
  try {
    // التحويل إلى أرقام
    const numericSellingPrice = Number(sellingPrice);
    const numericCostPrice = Number(costPrice);

    // التحقق من صحة البيانات
    if (isNaN(numericSellingPrice) || numericSellingPrice <= 0) {
      console.warn(`سعر البيع غير صالح: ${sellingPrice}`);
      return 0;
    }

    if (isNaN(numericCostPrice) || numericCostPrice <= 0) {
      console.warn(`سعر التكلفة غير صالح: ${costPrice}`);
      return 0;
    }

    // حساب نسبة الربح
    const profitMargin = ((numericSellingPrice - numericCostPrice) / numericSellingPrice) * 100;

    // تقريب نسبة الربح إلى رقمين عشريين
    return Math.round(profitMargin * 100) / 100;
  } catch (error) {
    console.error('خطأ في حساب نسبة الربح:', error);
    return 0;
  }
}

/**
 * حساب سعر البيع بناءً على سعر التكلفة ونسبة الربح المطلوبة
 * @param {number} costPrice - سعر التكلفة
 * @param {number} desiredMargin - نسبة الربح المطلوبة
 * @returns {number} - سعر البيع المقترح
 */
function calculateSellingPrice(costPrice, desiredMargin) {
  try {
    // التحويل إلى أرقام
    const numericCostPrice = Number(costPrice);
    const numericDesiredMargin = Number(desiredMargin);

    // التحقق من صحة البيانات
    if (isNaN(numericCostPrice) || numericCostPrice <= 0) {
      console.warn(`سعر التكلفة غير صالح: ${costPrice}`);
      return 0;
    }

    if (isNaN(numericDesiredMargin) || numericDesiredMargin < 0) {
      console.warn(`نسبة الربح المطلوبة غير صالحة: ${desiredMargin}`);
      return 0;
    }

    // حساب سعر البيع
    const sellingPrice = numericCostPrice / (1 - (numericDesiredMargin / 100));

    // تقريب سعر البيع إلى رقمين عشريين
    return Math.round(sellingPrice * 100) / 100;
  } catch (error) {
    console.error('خطأ في حساب سعر البيع:', error);
    return 0;
  }
}

/**
 * حساب توزيع المبلغ بين الرصيد الحالي والأرباح
 *
 * هذه الدالة تحسب كيفية توزيع المبلغ المضاف بين الرصيد الحالي والأرباح
 * بحيث لا يتجاوز الرصيد الحالي الرصيد الافتتاحي.
 *
 * المبدأ الأساسي هو:
 * 1. إذا كان الرصيد الحالي + المبلغ المضاف <= الرصيد الافتتاحي، يضاف كل المبلغ إلى الرصيد الحالي.
 * 2. إذا كان الرصيد الحالي + المبلغ المضاف > الرصيد الافتتاحي، يضاف فقط ما يكفي للوصول إلى الرصيد الافتتاحي،
 *    والباقي يذهب إلى الأرباح.
 *
 * ملاحظة: تم تعديل هذه الدالة لتنفيذ المتطلبات الجديدة:
 * - الرصيد الحالي لا يتجاوز الرصيد الافتتاحي أبداً
 * - أي مبلغ زائد عن الرصيد الافتتاحي يتم إضافته إلى الأرباح
 * - الأرباح تعكس فقط الفرق بين سعر البيع وسعر الشراء للمنتجات، بالإضافة إلى المبالغ الزائدة
 *
 * @param {number} amount - المبلغ المراد إضافته
 * @param {number} currentBalance - الرصيد الحالي
 * @param {number} initialBalance - الرصيد الافتتاحي
 * @returns {Object} - توزيع المبلغ
 */
function calculateBalanceDistribution(amount, currentBalance, initialBalance) {
  // التأكد من أن جميع المدخلات أرقام صالحة
  const numAmount = Number(amount) || 0;
  const numCurrentBalance = Number(currentBalance) || 0;
  const numInitialBalance = Number(initialBalance) || 0;

  // حساب المبلغ الذي يمكن إضافته إلى الرصيد الحالي
  // هذا هو الحد الأقصى للمبلغ الذي يمكن إضافته إلى الرصيد الحالي بحيث لا يتجاوز الرصيد الافتتاحي
  const maxAddableAmount = Math.max(0, numInitialBalance - numCurrentBalance);

  // المبلغ الذي سيضاف إلى الرصيد الحالي هو الأقل من:
  // 1. المبلغ المراد إضافته
  // 2. الحد الأقصى للمبلغ الذي يمكن إضافته
  const amountToAddToBalance = Math.min(numAmount, maxAddableAmount);

  // حساب المبلغ الزائد الذي سيذهب إلى الأرباح
  // هذا هو الفرق بين المبلغ المراد إضافته والمبلغ الذي سيضاف إلى الرصيد الحالي
  const excessAmount = Math.max(0, numAmount - amountToAddToBalance);

  return {
    amountToAddToBalance,  // المبلغ الذي سيضاف إلى الرصيد الحالي
    excessAmount,          // المبلغ الزائد الذي سيذهب إلى الأرباح
    hasExcess: excessAmount > 0  // مؤشر يدل على وجود مبلغ زائد
  };
}

/**
 * تحديث الخزينة بعد المعاملة
 * @param {Object} cashbox - الخزينة الحالية
 * @param {Object} transaction - المعاملة
 * @returns {Object} - الخزينة المحدثة
 */
function updateCashboxAfterTransaction(cashbox, transaction) {
  if (!cashbox || !transaction) {
    console.log('[PROFIT-FIX] خطأ: الخزينة أو المعاملة غير موجودة');
    return cashbox;
  }

  // نسخة جديدة من الخزينة
  const updatedCashbox = { ...cashbox };

  const { type, amount, profit = 0 } = transaction;
  const numAmount = Number(amount) || 0;
  const numProfit = Number(profit) || 0;

  console.log(`[PROFIT-FIX] تحديث الخزينة - النوع: ${type}, المبلغ: ${numAmount}, الربح: ${numProfit}`);
  console.log('[PROFIT-FIX] قيم الخزينة قبل التحديث:', {
    current_balance: updatedCashbox.current_balance,
    profit_total: updatedCashbox.profit_total,
    sales_total: updatedCashbox.sales_total,
    purchases_total: updatedCashbox.purchases_total,
    returns_total: updatedCashbox.returns_total
  });

  if (type === 'income' || type === 'sale') {
    // في حالة الدخل أو البيع
    console.log(`[PROFIT-FIX] معاملة دخل/بيع - المبلغ: ${numAmount}, الربح: ${numProfit}`);

    if (type === 'income') {
      // في حالة الدخل (إضافة مبلغ للخزينة)، نطبق قاعدة عدم تجاوز الرصيد الافتتاحي
      // حساب توزيع المبلغ بين الرصيد الحالي والأرباح
      const { amountToAddToBalance, excessAmount } = calculateBalanceDistribution(
        numAmount,
        updatedCashbox.current_balance,
        updatedCashbox.initial_balance
      );

      console.log(`[PROFIT-FIX] توزيع المبلغ - للرصيد: ${amountToAddToBalance}, للأرباح: ${excessAmount}`);

      // تحديث الرصيد الحالي (لا يتجاوز الرصيد الافتتاحي)
      updatedCashbox.current_balance += amountToAddToBalance;

      // إضافة المبلغ الزائد إلى الأرباح
      updatedCashbox.profit_total += excessAmount;

      // تحديث إجمالي المبيعات
      updatedCashbox.sales_total += numAmount;

      // إضافة رسالة توضيحية إذا كان هناك مبلغ زائد
      if (excessAmount > 0) {
        console.log(`[PROFIT-FIX] تم إضافة ${amountToAddToBalance} إلى الرصيد الحالي و ${excessAmount} إلى الأرباح`);
      }
    } else {
      // في حالة البيع، نضيف المبلغ كاملاً إلى الرصيد الحالي
      updatedCashbox.current_balance += numAmount;

      // تحديث إجمالي الأرباح - التأكد من أن الربح لا يكون سالباً
      // الربح هنا هو الفرق بين سعر البيع وسعر الشراء فقط
      // تصحيح: التأكد من أن الربح يحسب بشكل صحيح كفرق بين سعر البيع وسعر الشراء
      updatedCashbox.profit_total += Math.max(0, numProfit);

      // تحديث إجمالي المبيعات
      updatedCashbox.sales_total += numAmount;
    }
  } else if (type === 'expense' || type === 'purchase') {
    // في حالة المصروفات أو الشراء
    console.log(`[PROFIT-FIX] معاملة مصروفات/شراء - المبلغ: ${numAmount}`);

    // تحديث الرصيد الحالي
    updatedCashbox.current_balance -= numAmount;

    // تحديث إجمالي المشتريات
    updatedCashbox.purchases_total += numAmount;
  } else if (type === 'return') {
    // في حالة الإرجاع
    console.log(`[PROFIT-FIX] معاملة إرجاع - المبلغ: ${numAmount}`);

    // تحديث الرصيد الحالي بخصم المبلغ المرجع (لأننا نعيد المال للعميل)
    updatedCashbox.current_balance -= numAmount;

    // تقليل إجمالي المبيعات بالمبلغ المرجع
    updatedCashbox.sales_total -= numAmount;

    // زيادة إجمالي المرتجعات
    updatedCashbox.returns_total = (updatedCashbox.returns_total || 0) + numAmount;

    // تحديث إجمالي الأرباح (خصم الربح المرتبط بالمبيعات المرتجعة)
    // نفترض أن الربح كان 20% من قيمة المبيعات إذا لم يتم تحديده
    const returnProfit = numProfit > 0 ? numProfit : (numAmount * 0.2);
    updatedCashbox.profit_total = Math.max(0, updatedCashbox.profit_total - returnProfit);
  }

  console.log('[PROFIT-FIX] قيم الخزينة بعد التحديث:', {
    current_balance: updatedCashbox.current_balance,
    profit_total: updatedCashbox.profit_total,
    sales_total: updatedCashbox.sales_total,
    purchases_total: updatedCashbox.purchases_total,
    returns_total: updatedCashbox.returns_total
  });

  return updatedCashbox;
}

/**
 * إنشاء خزينة جديدة
 * @param {number} initialBalance - الرصيد الافتتاحي
 * @returns {Object} - الخزينة الجديدة
 */
function createNewCashbox(initialBalance = 0) {
  const numInitialBalance = Number(initialBalance) || 0;

  return {
    id: 1,
    initial_balance: numInitialBalance,
    current_balance: numInitialBalance,
    profit_total: 0,
    sales_total: 0,
    purchases_total: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    exists: true
  };
}

/**
 * إصلاح قيم الخزينة السالبة
 * @param {Object} cashbox - الخزينة الحالية
 * @returns {Object} - الخزينة المصححة
 */
function fixNegativeCashboxValues(cashbox) {
  if (!cashbox) {
    return cashbox;
  }

  console.log('[PROFIT-FIX] إصلاح قيم الخزينة السالبة');
  console.log('[PROFIT-FIX] قيم الخزينة قبل الإصلاح:', {
    initial_balance: cashbox.initial_balance,
    current_balance: cashbox.current_balance,
    profit_total: cashbox.profit_total,
    sales_total: cashbox.sales_total,
    purchases_total: cashbox.purchases_total,
    returns_total: cashbox.returns_total
  });

  // نسخة جديدة من الخزينة
  const fixedCashbox = { ...cashbox };

  // تصحيح القيم السالبة
  fixedCashbox.profit_total = Math.max(0, Number(fixedCashbox.profit_total) || 0);
  fixedCashbox.sales_total = Math.max(0, Number(fixedCashbox.sales_total) || 0);
  fixedCashbox.purchases_total = Math.max(0, Number(fixedCashbox.purchases_total) || 0);
  fixedCashbox.returns_total = Math.max(0, Number(fixedCashbox.returns_total) || 0);

  // التأكد من أن الرصيد الحالي والرصيد الافتتاحي ليسا سالبين
  fixedCashbox.current_balance = Math.max(0, Number(fixedCashbox.current_balance) || 0);
  fixedCashbox.initial_balance = Math.max(0, Number(fixedCashbox.initial_balance) || 0);

  console.log('[PROFIT-FIX] قيم الخزينة بعد الإصلاح:', {
    initial_balance: fixedCashbox.initial_balance,
    current_balance: fixedCashbox.current_balance,
    profit_total: fixedCashbox.profit_total,
    sales_total: fixedCashbox.sales_total,
    purchases_total: fixedCashbox.purchases_total,
    returns_total: fixedCashbox.returns_total
  });

  return fixedCashbox;
}

/**
 * إعادة حساب الأرباح بناءً على الرصيد الافتتاحي والرصيد الحالي
 *
 * هذه الدالة تستخدم عند تغيير الرصيد الافتتاحي لإعادة حساب الأرباح بشكل صحيح
 *
 * @param {number} initialBalance - الرصيد الافتتاحي
 * @param {number} currentBalance - الرصيد الحالي
 * @param {number} salesTotal - إجمالي المبيعات
 * @param {number} purchasesTotal - إجمالي المشتريات
 * @returns {Object} - نتائج إعادة الحساب
 */
function recalculateProfitAfterInitialBalanceChange(initialBalance, currentBalance, salesTotal, purchasesTotal = 0) {
  // التأكد من أن جميع المدخلات أرقام صالحة
  const numInitialBalance = Number(initialBalance) || 0;
  const numCurrentBalance = Number(currentBalance) || 0;
  const numSalesTotal = Number(salesTotal) || 0;
  const numPurchasesTotal = Number(purchasesTotal) || 0;

  console.log(`[PROFIT-FIX] إعادة حساب الأرباح - الرصيد الافتتاحي: ${numInitialBalance}, الرصيد الحالي: ${numCurrentBalance}, إجمالي المبيعات: ${numSalesTotal}, إجمالي المشتريات: ${numPurchasesTotal}`);

  // تطبيق قاعدة عدم تجاوز الرصيد الحالي للرصيد الافتتاحي
  let newCurrentBalance = numCurrentBalance;
  let excessToProfit = 0;

  // إذا كان الرصيد الحالي أكبر من الرصيد الافتتاحي
  if (numCurrentBalance > numInitialBalance) {
    // حساب المبلغ الزائد الذي سيتم نقله إلى الأرباح
    excessToProfit = numCurrentBalance - numInitialBalance;
    // تعديل الرصيد الحالي ليساوي الرصيد الافتتاحي
    newCurrentBalance = numInitialBalance;

    console.log(`[PROFIT-FIX] الرصيد الحالي (${numCurrentBalance}) أكبر من الرصيد الافتتاحي (${numInitialBalance})`);
    console.log(`[PROFIT-FIX] سيتم نقل ${excessToProfit} من الرصيد الحالي إلى الأرباح`);
  }

  // حساب الربح الإجمالي الجديد بطريقة صحيحة
  // الربح = (إجمالي المبيعات - إجمالي المشتريات) + المبلغ الزائد من الرصيد
  // تصحيح: التأكد من أن الربح يحسب كفرق بين المبيعات والمشتريات وليس مجموعهما
  const profitFromSales = Math.max(0, numSalesTotal - numPurchasesTotal);
  const newProfit = profitFromSales + excessToProfit;

  console.log(`[PROFIT-FIX] نتائج إعادة الحساب - الرصيد الجديد: ${newCurrentBalance}, إجمالي المبيعات: ${numSalesTotal}, إجمالي المشتريات: ${numPurchasesTotal}, الربح من المبيعات: ${profitFromSales}, الربح من الرصيد الزائد: ${excessToProfit}, إجمالي الربح الجديد: ${newProfit}`);

  return {
    newCurrentBalance,
    excessToProfit,
    newProfit  // الربح لا يمكن أن يكون سالباً بسبب استخدام Math.max
  };
}

/**
 * إعادة حساب إجمالي الأرباح بناءً على المبيعات والمشتريات
 *
 * هذه الدالة تستخدم لإصلاح حساب الأرباح في الخزينة
 *
 * @param {number} salesTotal - إجمالي المبيعات
 * @param {number} purchasesTotal - إجمالي المشتريات
 * @param {number} excessBalance - المبلغ الزائد من الرصيد (اختياري)
 * @returns {number} - إجمالي الأرباح المحسوب بشكل صحيح
 */
function recalculateTotalProfit(salesTotal, purchasesTotal, excessBalance = 0) {
  // التأكد من أن جميع المدخلات أرقام صالحة
  const numSalesTotal = Number(salesTotal) || 0;
  const numPurchasesTotal = Number(purchasesTotal) || 0;
  const numExcessBalance = Number(excessBalance) || 0;

  console.log(`[PROFIT-FIX] إعادة حساب إجمالي الأرباح - إجمالي المبيعات: ${numSalesTotal}, إجمالي المشتريات: ${numPurchasesTotal}, المبلغ الزائد: ${numExcessBalance}`);

  // حساب الربح من المبيعات (الفرق بين إجمالي المبيعات وإجمالي المشتريات)
  // تصحيح: التأكد من أن الربح يحسب كفرق بين المبيعات والمشتريات وليس مجموعهما
  const profitFromSales = Math.max(0, numSalesTotal - numPurchasesTotal);

  // إجمالي الربح = الربح من المبيعات + المبلغ الزائد من الرصيد
  const totalProfit = profitFromSales + numExcessBalance;

  console.log(`[PROFIT-FIX] نتائج إعادة حساب إجمالي الأرباح - الربح من المبيعات: ${profitFromSales}, المبلغ الزائد: ${numExcessBalance}, إجمالي الربح: ${totalProfit}`);

  return totalProfit;
}

// تصدير الدوال
module.exports = {
  calculateProfit,
  calculateProfitMargin,
  calculateSellingPrice,
  calculateBalanceDistribution,
  updateCashboxAfterTransaction,
  createNewCashbox,
  fixNegativeCashboxValues,
  recalculateProfitAfterInitialBalanceChange,
  recalculateTotalProfit
};
