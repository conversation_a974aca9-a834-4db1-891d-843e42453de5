/**
 * نظام الأحداث المركزي
 * يوفر واجهة موحدة لإرسال واستقبال الإشعارات بين العمليات الخلفية وواجهة المستخدم
 */

const { ipcMain, BrowserWindow } = require('electron');
const { logSystem, logError } = require('../../error-handler');

// قائمة بجميع أنواع الأحداث المدعومة
const EventTypes = {
  // أحداث المخزون
  INVENTORY_UPDATED: 'inventory-updated',
  INVENTORY_ITEM_ADDED: 'inventory-item-added',
  INVENTORY_ITEM_UPDATED: 'inventory-item-updated',
  INVENTORY_ITEM_DELETED: 'inventory-item-deleted',

  // أحداث الأصناف
  ITEM_ADDED: 'item-added',
  ITEM_UPDATED: 'item-updated',
  ITEM_DELETED: 'item-deleted',

  // أحداث المعاملات
  TRANSACTION_ADDED: 'transaction-added',
  TRANSACTION_UPDATED: 'transaction-updated',
  TRANSACTION_DELETED: 'transaction-deleted',

  // أحداث العملاء
  CUSTOMER_ADDED: 'customer-added',
  CUSTOMER_UPDATED: 'customer-updated',
  CUSTOMER_DELETED: 'customer-deleted',

  // أحداث الخزينة
  CASHBOX_UPDATED: 'cashbox-updated',
  CASHBOX_TRANSACTION_ADDED: 'cashbox-transaction-added',

  // أحداث عامة
  DATA_CHANGED: 'data-changed',
  REFRESH_NEEDED: 'refresh-needed'
};

/**
 * إرسال حدث إلى واجهة المستخدم
 * @param {string} eventType - نوع الحدث
 * @param {Object} data - البيانات المرتبطة بالحدث
 */
function sendEvent(eventType, data = {}) {
  try {
    // التحقق من وجود النافذة الرئيسية
    if (!global.mainWindow || !global.mainWindow.webContents) {
      logSystem(`لا يمكن إرسال الحدث ${eventType} لأن النافذة الرئيسية غير متوفرة`, 'warning');
      return;
    }

    // إضافة وقت الحدث
    const eventData = {
      ...data,
      timestamp: new Date().toISOString()
    };

    // تسجيل معلومات الحدث
    logSystem(`إرسال حدث ${eventType} مع البيانات: ${JSON.stringify(eventData)}`, 'info');

    // إرسال الحدث إلى واجهة المستخدم
    global.mainWindow.webContents.send(eventType, eventData);

    // إرسال حدث عام للتحديث
    global.mainWindow.webContents.send(EventTypes.DATA_CHANGED, {
      source: eventType,
      data: eventData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logError(error, `sendEvent-${eventType}`);
  }
}

/**
 * تسجيل معالج لاستقبال الأحداث من واجهة المستخدم
 * @param {string} eventType - نوع الحدث
 * @param {Function} handler - دالة المعالجة
 */
function registerHandler(eventType, handler) {
  try {
    ipcMain.handle(eventType, async (event, ...args) => {
      try {
        return await handler(...args);
      } catch (error) {
        logError(error, `handler-${eventType}`);
        throw error;
      }
    });

    logSystem(`تم تسجيل معالج للحدث ${eventType} بنجاح`, 'info');
  } catch (error) {
    logError(error, `registerHandler-${eventType}`);
  }
}

/**
 * إرسال إشعار بتحديث المخزون
 * @param {Object} data - بيانات التحديث
 */
function notifyInventoryUpdated(data = {}) {
  sendEvent(EventTypes.INVENTORY_UPDATED, data);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'inventory' });
}

/**
 * إرسال إشعار بإضافة صنف جديد
 * @param {Object} item - بيانات الصنف الجديد
 */
function notifyItemAdded(item) {
  logSystem(`إرسال إشعار بإضافة صنف جديد: ${JSON.stringify(item)}`, 'info');

  // إرسال إشعار بإضافة صنف جديد
  sendEvent(EventTypes.ITEM_ADDED, item);

  // إرسال إشعار بالحاجة لتحديث قائمة الأصناف
  sendEvent(EventTypes.REFRESH_NEEDED, {
    target: 'items',
    operation: 'add-item',
    itemId: item.id,
    timestamp: new Date().toISOString()
  });

  // إرسال إشعار إضافي للتأكد من تحديث واجهة المستخدم
  setTimeout(() => {
    sendEvent(EventTypes.REFRESH_NEEDED, {
      target: 'items',
      operation: 'add-item-delayed',
      itemId: item.id,
      timestamp: new Date().toISOString()
    });
  }, 500);
}

/**
 * إرسال إشعار بتحديث صنف
 * @param {Object} item - بيانات الصنف المحدث
 */
function notifyItemUpdated(item) {
  sendEvent(EventTypes.ITEM_UPDATED, item);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'items' });
}

/**
 * إرسال إشعار بحذف صنف
 * @param {Object} item - بيانات الصنف المحذوف
 */
function notifyItemDeleted(item) {
  sendEvent(EventTypes.ITEM_DELETED, item);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'items' });
}

/**
 * إرسال إشعار بإضافة معاملة جديدة
 * @param {Object} transaction - بيانات المعاملة الجديدة
 */
function notifyTransactionAdded(transaction) {
  sendEvent(EventTypes.TRANSACTION_ADDED, transaction);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'transactions' });

  // تحديث المخزون لأن المعاملات تؤثر على المخزون
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'inventory' });
}

/**
 * إرسال إشعار بإضافة عميل جديد
 * @param {Object} customer - بيانات العميل الجديد
 */
function notifyCustomerAdded(customer) {
  sendEvent(EventTypes.CUSTOMER_ADDED, customer);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'customers' });
}

/**
 * إرسال إشعار بتحديث عميل
 * @param {Object} customer - بيانات العميل المحدث
 */
function notifyCustomerUpdated(customer) {
  sendEvent(EventTypes.CUSTOMER_UPDATED, customer);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'customers' });
}

/**
 * إرسال إشعار بتحديث الخزينة
 * @param {Object} data - بيانات تحديث الخزينة
 */
function notifyCashboxUpdated(data) {
  sendEvent(EventTypes.CASHBOX_UPDATED, data);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'cashbox' });
}

/**
 * إرسال إشعار بإضافة عملية إرجاع جديدة
 * @param {Object} returnTransaction - بيانات عملية الإرجاع الجديدة
 */
function notifyReturnTransactionAdded(returnTransaction) {
  // استخدام نفس حدث إضافة المعاملة لأن الإرجاع هو نوع من المعاملات
  sendEvent(EventTypes.TRANSACTION_ADDED, {
    ...returnTransaction,
    transaction_type: 'return'
  });

  // تحديث المخزون لأن عمليات الإرجاع تؤثر على المخزون
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'inventory' });

  // تحديث المعاملات
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'transactions' });
}

// تصدير الدوال والثوابت
module.exports = {
  EventTypes,
  sendEvent,
  registerHandler,
  notifyInventoryUpdated,
  notifyItemAdded,
  notifyItemUpdated,
  notifyItemDeleted,
  notifyTransactionAdded,
  notifyCustomerAdded,
  notifyCustomerUpdated,
  notifyCashboxUpdated,
  notifyReturnTransactionAdded
};


