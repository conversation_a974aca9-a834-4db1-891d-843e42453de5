/**
 * وحدة إدارة الخزينة المحسنة
 * تقوم هذه الوحدة بإدارة جميع عمليات الخزينة بشكل موحد
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');
const eventSystem = require('./event-system');
const cashboxUtils = require('./utils/profitCalculator');

// مرجع لقاعدة البيانات (سيتم الحصول عليه من مدير قاعدة البيانات)
let db = null;

/**
 * تهيئة وحدة إدارة الخزينة
 */
function initialize() {
  try {
    console.log('جاري تهيئة وحدة إدارة الخزينة...');

    // الحصول على اتصال قاعدة البيانات من مدير قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    console.log('تم تهيئة وحدة إدارة الخزينة بنجاح');
    logSystem('تم تهيئة وحدة إدارة الخزينة بنجاح', 'info');

    return true;
  } catch (error) {
    console.error('خطأ في تهيئة وحدة إدارة الخزينة:', error);
    logError(error, 'initialize - cashbox-manager');
    return false;
  }
}

/**
 * الحصول على معلومات الخزينة
 * @returns {Object} - معلومات الخزينة
 */
function getCashbox() {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log('[CASHBOX-FIX] استدعاء getCashbox');

    const stmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
    const cashbox = stmt.get();

    if (!cashbox) {
      console.log('[CASHBOX-FIX] لم يتم العثور على خزينة');
      return { exists: false };
    }

    console.log('[CASHBOX-FIX] تم العثور على خزينة:', cashbox);
    console.log('[CASHBOX-FIX] قيم الخزينة:');
    console.log('- الرصيد الافتتاحي:', cashbox.initial_balance);
    console.log('- الرصيد الحالي:', cashbox.current_balance);
    console.log('- إجمالي الأرباح:', cashbox.profit_total);
    console.log('- إجمالي المبيعات:', cashbox.sales_total);
    console.log('- إجمالي المشتريات:', cashbox.purchases_total);
    console.log('- إجمالي المرتجعات:', cashbox.returns_total || 0);

    const result = {
      exists: true,
      id: cashbox.id,
      initial_balance: cashbox.initial_balance,
      current_balance: cashbox.current_balance,
      profit_total: cashbox.profit_total,
      sales_total: cashbox.sales_total,
      purchases_total: cashbox.purchases_total,
      returns_total: cashbox.returns_total || 0,
      transport_total: cashbox.transport_total || 0,
      created_at: cashbox.created_at,
      updated_at: cashbox.updated_at
    };

    console.log('[CASHBOX-FIX] إرجاع نتيجة getCashbox:', result);
    return result;
  } catch (error) {
    console.error('[CASHBOX-FIX] خطأ في getCashbox:', error);
    logError(error, 'getCashbox');
    return { exists: false, error: error.message };
  }
}

/**
 * إنشاء خزينة جديدة
 * @param {number} initialBalance - الرصيد الافتتاحي
 * @returns {Object} - نتيجة العملية
 */
function createCashbox(initialBalance) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحويل إلى رقم
    const numericInitialBalance = Number(initialBalance);
    if (isNaN(numericInitialBalance) || numericInitialBalance < 0) {
      return { success: false, error: `الرصيد الافتتاحي غير صالح: ${initialBalance}` };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من عدم وجود خزينة
      const checkStmt = db.prepare('SELECT COUNT(*) as count FROM cashbox');
      const { count } = checkStmt.get();

      if (count > 0) {
        throw new Error('توجد خزينة بالفعل');
      }

      // إنشاء الخزينة
      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT INTO cashbox (
          initial_balance, current_balance, profit_total, sales_total, purchases_total, returns_total,
          created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertStmt.run(
        numericInitialBalance,
        numericInitialBalance,
        0, // إجمالي الربح
        0, // إجمالي المبيعات
        0, // إجمالي المشتريات
        0, // إجمالي المرتجعات
        now,
        now
      );

      // الحصول على الخزينة المنشأة
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const cashbox = getCashboxStmt.get();

      // إرسال إشعار بتحديث الخزينة
      const cashboxData = {
        exists: true,
        id: cashbox.id,
        initial_balance: cashbox.initial_balance,
        current_balance: cashbox.current_balance,
        profit_total: cashbox.profit_total,
        sales_total: cashbox.sales_total,
        purchases_total: cashbox.purchases_total,
        returns_total: cashbox.returns_total || 0,
        transport_total: cashbox.transport_total || 0,
        created_at: cashbox.created_at,
        updated_at: cashbox.updated_at
      };

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: cashbox.id,
        current_balance: cashbox.current_balance,
        sales_total: cashbox.sales_total,
        purchases_total: cashbox.purchases_total,
        returns_total: cashbox.returns_total || 0,
        transport_total: cashbox.transport_total || 0,
        profit_total: cashbox.profit_total,
        success: true
      });

      return {
        success: true,
        cashbox: cashboxData
      };
    })();
  } catch (error) {
    console.error('خطأ في إنشاء الخزينة:', error);
    logError(error, 'createCashbox');
    return { success: false, error: error.message };
  }
}

/**
 * تحديث الرصيد الافتتاحي للخزينة
 * @param {number} initialBalance - الرصيد الافتتاحي الجديد
 * @returns {Object} - نتيجة العملية
 */
function updateInitialBalance(initialBalance) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحويل إلى رقم
    const numericInitialBalance = Number(initialBalance);
    if (isNaN(numericInitialBalance) || numericInitialBalance < 0) {
      return { success: false, error: `الرصيد الافتتاحي غير صالح: ${initialBalance}` };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من وجود الخزينة
      const checkStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const cashbox = checkStmt.get();

      if (!cashbox) {
        throw new Error('الخزينة غير موجودة');
      }

      // حساب الفرق بين الرصيد الافتتاحي الجديد والقديم
      const oldInitialBalance = cashbox.initial_balance;
      const difference = numericInitialBalance - oldInitialBalance;

      // تسجيل معلومات للتشخيص
      console.log(`[CASHBOX-FIX] تحديث الرصيد الافتتاحي: القديم=${oldInitialBalance}, الجديد=${numericInitialBalance}, الفرق=${difference}`);
      console.log(`[CASHBOX-FIX] قيم الخزينة قبل التحديث: الرصيد الحالي=${cashbox.current_balance}, الأرباح=${cashbox.profit_total}`);

      // استخدام الدالة الجديدة لإعادة حساب الأرباح بناءً على الرصيد الافتتاحي الجديد
      // أولاً، نحدث الرصيد الحالي بإضافة الفرق بين الرصيد الافتتاحي الجديد والقديم
      let updatedCurrentBalance = cashbox.current_balance;

      if (difference > 0) {
        // إذا كان الرصيد الافتتاحي الجديد أكبر من القديم، نضيف الفرق إلى الرصيد الحالي
        updatedCurrentBalance += difference;
      } else if (difference < 0) {
        // إذا كان الرصيد الافتتاحي الجديد أقل من القديم، نقلل الرصيد الحالي بمقدار الفرق، ولكن لا نجعله أقل من صفر
        updatedCurrentBalance = Math.max(0, updatedCurrentBalance + difference);
      }

      console.log(`[CASHBOX-FIX] الرصيد الحالي بعد تطبيق الفرق: ${updatedCurrentBalance}`);

      // استخدام الدالة الجديدة لإعادة حساب الأرباح
      // تم تعديل هذا الجزء لإزالة القيد على الرصيد الحالي بحيث يمكن أن يتجاوز الرصيد الافتتاحي
      const { newCurrentBalance, excessToProfit, newProfit } = cashboxUtils.recalculateProfitAfterInitialBalanceChange(
        numericInitialBalance,
        updatedCurrentBalance,
        cashbox.sales_total
      );

      console.log(`[CASHBOX-FIX] نتائج إعادة حساب الأرباح:`);
      console.log(`- الرصيد الحالي الجديد: ${newCurrentBalance}`);
      console.log(`- الربح الإجمالي الجديد: ${newProfit}`);

      // تحديد تعديل الأرباح بناءً على الفرق بين الربح الحالي والربح الجديد
      const profitAdjustment = newProfit - cashbox.profit_total;

      console.log(`[CASHBOX-FIX] تعديل الأرباح: ${profitAdjustment} (الربح الحالي: ${cashbox.profit_total}, الربح الجديد: ${newProfit})`);

      // تم إزالة التحقق من وجود فائض للأرباح لأن الرصيد الحالي يمكن أن يتجاوز الرصيد الافتتاحي

      // تحديث الخزينة
      const now = new Date().toISOString();
      const updateStmt = db.prepare(`
        UPDATE cashbox
        SET initial_balance = ?,
            current_balance = ?,
            profit_total = ?,
            updated_at = ?
        WHERE id = ?
      `);

      updateStmt.run(
        numericInitialBalance,
        newCurrentBalance,
        newProfit,  // استخدام قيمة الربح الجديدة المحسوبة بدلاً من إضافة تعديل للقيمة الحالية
        now,
        cashbox.id
      );

      // إعادة حساب الأرباح تلقائيًا بعد تحديث الرصيد الافتتاحي
      // هذا يضمن أن أي مبلغ زائد عن الرصيد الافتتاحي سيتم نقله إلى الأرباح تلقائيًا
      console.log('[CASHBOX-FIX] إعادة حساب الأرباح تلقائيًا بعد تحديث الرصيد الافتتاحي');

      // استدعاء دالة fixProfitCalculation داخليًا (بدون استخدام API)
      const fixResult = fixProfitCalculationInternal();

      // الحصول على الخزينة المحدثة
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const updatedCashbox = getCashboxStmt.get();

      // تسجيل معلومات للتشخيص بعد التحديث
      console.log(`[CASHBOX-FIX] قيم الخزينة بعد التحديث: الرصيد الافتتاحي=${updatedCashbox.initial_balance}, الرصيد الحالي=${updatedCashbox.current_balance}, الأرباح=${updatedCashbox.profit_total}`);
      logSystem(`تم تحديث الرصيد الافتتاحي للخزينة من ${oldInitialBalance} إلى ${numericInitialBalance} مع تعديل الرصيد الحالي إلى ${updatedCashbox.current_balance} وتعديل الأرباح بمقدار ${profitAdjustment}`, 'info');

      // إرسال إشعار بتحديث الخزينة
      const cashboxData = {
        exists: true,
        id: updatedCashbox.id,
        initial_balance: updatedCashbox.initial_balance,
        current_balance: updatedCashbox.current_balance,
        profit_total: updatedCashbox.profit_total,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        created_at: updatedCashbox.created_at,
        updated_at: updatedCashbox.updated_at
      };

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: updatedCashbox.id,
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        profit_total: updatedCashbox.profit_total,
        success: true
      });

      return {
        success: true,
        cashbox: cashboxData
      };
    })();
  } catch (error) {
    console.error('خطأ في تحديث الرصيد الافتتاحي:', error);
    logError(error, 'updateInitialBalance');
    return { success: false, error: error.message };
  }
}

/**
 * إضافة معاملة للخزينة
 * @param {Object} transaction - بيانات المعاملة
 * @returns {Object} - نتيجة العملية
 */
function addTransaction(transaction) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة البيانات
    if (!transaction.type || (transaction.type !== 'income' && transaction.type !== 'expense')) {
      return { success: false, error: 'نوع المعاملة غير صالح' };
    }

    // التحويل إلى رقم
    const numericAmount = Number(transaction.amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return { success: false, error: `المبلغ غير صالح: ${transaction.amount}` };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من وجود الخزينة
      const checkStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const cashbox = checkStmt.get();

      // إذا لم تكن هناك خزينة، قم بإنشاء واحدة
      if (!cashbox) {
        const now = new Date().toISOString();
        const createStmt = db.prepare(`
          INSERT INTO cashbox (
            initial_balance, current_balance, profit_total, sales_total, purchases_total, returns_total,
            created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);

        createStmt.run(
          0, // الرصيد الافتتاحي
          0, // الرصيد الحالي
          0, // إجمالي الربح
          0, // إجمالي المبيعات
          0, // إجمالي المشتريات
          0, // إجمالي المرتجعات
          now,
          now
        );
      }

      // إضافة المعاملة
      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT INTO cashbox_transactions (
          type, amount, source, notes, user_id, created_at
        )
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      insertStmt.run(
        transaction.type,
        numericAmount,
        transaction.source || '',
        transaction.notes || '',
        transaction.user_id || null,
        now
      );

      // تحديث الخزينة
      if (transaction.type === 'income') {
        // في حالة الدخل، نطبق قاعدة عدم تجاوز الرصيد الافتتاحي
        // استخدام دالة توزيع المبلغ بين الرصيد الحالي والأرباح
        const { amountToAddToBalance, excessAmount } = cashboxUtils.calculateBalanceDistribution(
          numericAmount,
          cashbox.current_balance,
          cashbox.initial_balance
        );

        console.log(`[CASHBOX-FIX] توزيع المبلغ - للرصيد: ${amountToAddToBalance}, للأرباح: ${excessAmount}`);

        // تحديث الخزينة بإضافة المبلغ المحسوب إلى الرصيد الحالي والمبلغ الزائد إلى الأرباح
        const updateStmt = db.prepare(`
          UPDATE cashbox
          SET current_balance = current_balance + ?,
              sales_total = sales_total + ?,
              profit_total = profit_total + ?,
              updated_at = ?
          WHERE id = ?
        `);

        updateStmt.run(
          amountToAddToBalance,
          numericAmount,
          excessAmount,
          now,
          cashbox ? cashbox.id : 1
        );

        // تسجيل رسالة توضيحية
        if (excessAmount > 0) {
          logSystem(`تم إضافة ${amountToAddToBalance} إلى الرصيد الحالي و ${excessAmount} إلى الأرباح.`, 'info');
        } else {
          logSystem(`تم إضافة ${amountToAddToBalance} إلى الرصيد الحالي.`, 'info');
        }
      } else {
        // في حالة المصروفات، نخصم المبلغ من الرصيد الحالي
        // التحقق من نوع المعاملة لتحديد الحقل المناسب للتحديث
        if (transaction.source === 'transport') {
          // إذا كانت معاملة مصاريف نقل، نحدث transport_total
          const updateStmt = db.prepare(`
            UPDATE cashbox
            SET current_balance = current_balance - ?,
                transport_total = transport_total + ?,
                updated_at = ?
            WHERE id = ?
          `);

          updateStmt.run(
            numericAmount,
            numericAmount,
            now,
            cashbox ? cashbox.id : 1
          );
        } else {
          // للمصروفات العادية، نحدث purchases_total
          const updateStmt = db.prepare(`
            UPDATE cashbox
            SET current_balance = current_balance - ?,
                purchases_total = purchases_total + ?,
                updated_at = ?
            WHERE id = ?
          `);

          updateStmt.run(
            numericAmount,
            numericAmount,
            now,
            cashbox ? cashbox.id : 1
          );
        }
      }

      // إعادة حساب الأرباح تلقائيًا بعد كل عملية
      // هذا يضمن أن أي مبلغ زائد عن الرصيد الافتتاحي سيتم نقله إلى الأرباح تلقائيًا
      console.log('[CASHBOX-FIX] إعادة حساب الأرباح تلقائيًا بعد إضافة المعاملة');

      // استدعاء دالة fixProfitCalculation داخليًا (بدون استخدام API)
      // هذا يضمن أن الأرباح سيتم حسابها بشكل صحيح بعد كل عملية
      const fixResult = fixProfitCalculationInternal();

      // الحصول على الخزينة المحدثة بعد إعادة حساب الأرباح
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const updatedCashbox = getCashboxStmt.get();

      // إرسال إشعار بتحديث الخزينة
      const cashboxData = {
        exists: true,
        id: updatedCashbox.id,
        initial_balance: updatedCashbox.initial_balance,
        current_balance: updatedCashbox.current_balance,
        profit_total: updatedCashbox.profit_total,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        created_at: updatedCashbox.created_at,
        updated_at: updatedCashbox.updated_at
      };

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: updatedCashbox.id,
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        transport_total: updatedCashbox.transport_total || 0,
        profit_total: updatedCashbox.profit_total,
        transaction_type: transaction.type,
        amount: numericAmount,
        success: true
      });

      return {
        success: true,
        cashbox: cashboxData
      };
    })();
  } catch (error) {
    console.error('خطأ في إضافة معاملة للخزينة:', error);
    logError(error, 'addTransaction');
    return { success: false, error: error.message };
  }
}

/**
 * الحصول على معاملات الخزينة
 * @param {Object} filters - مرشحات البحث
 * @returns {Array} - قائمة المعاملات
 */
function getTransactions(filters = {}) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log(`[CASHBOX-FIX] بدء الحصول على معاملات الخزينة مع الفلاتر:`, filters);

    // استعلام للحصول على معاملات الخزينة المباشرة (من جدول cashbox_transactions)
    let cashboxQuery = `
      SELECT
        ct.id,
        ct.type,
        ct.amount,
        ct.source,
        ct.notes,
        ct.user_id,
        ct.created_at,
        u.username as user_name,
        'cashbox_transaction' as transaction_source
      FROM cashbox_transactions ct
      LEFT JOIN users u ON ct.user_id = u.id
    `;

    // استعلام للحصول على معاملات البيع والشراء والاسترجاع (من جدول transactions)
    // استخدام GROUP BY invoice_number لتجميع المعاملات حسب الفاتورة
    let transactionsQuery = `
      SELECT
        MIN(t.id) as id,
        t.transaction_type as type,
        SUM(t.total_price) as amount,
        t.transaction_type as source,
        COALESCE(t.notes, t.invoice_number) as notes,
        t.user_id,
        MAX(t.transaction_date) as created_at,
        u.username as user_name,
        '' as item_name,
        SUM(t.quantity) as quantity,
        'transaction' as transaction_source,
        t.invoice_number,
        COUNT(t.id) as items_count
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      WHERE t.transaction_type IN ('sale', 'purchase', 'return')
      GROUP BY t.invoice_number, t.transaction_type
    `;

    const cashboxQueryParams = [];
    const transactionsQueryParams = [];
    const cashboxConditions = [];
    const transactionsConditions = [];

    // إضافة الفلاتر لاستعلام معاملات الخزينة
    if (filters.type) {
      cashboxConditions.push('ct.type = ?');
      cashboxQueryParams.push(filters.type);

      // تحويل نوع المعاملة للاستعلام الثاني
      if (filters.type === 'income') {
        transactionsConditions.push("t.transaction_type = 'sale'");
      } else if (filters.type === 'expense') {
        transactionsConditions.push("t.transaction_type = 'purchase'");
      } else if (filters.type === 'return') {
        transactionsConditions.push("t.transaction_type = 'return'");
        console.log('[CASHBOX-FIX] تم تطبيق فلتر الاسترجاع على استعلام المعاملات');
      }
    }

    // إضافة فلتر groupBySource إذا كان موجودًا
    if (filters.groupBySource) {
      console.log('[CASHBOX-FIX] تم تطبيق فلتر groupBySource');
    }

    // إضافة فلتر groupByDate إذا كان موجودًا
    if (filters.groupByDate) {
      console.log('[CASHBOX-FIX] تم تطبيق فلتر groupByDate');
    }

    // تم تعديل هذا الجزء لعرض جميع المعاملات بغض النظر عن المصدر
    if (filters.source && filters.source !== '') {
      cashboxConditions.push('ct.source = ?');
      cashboxQueryParams.push(filters.source);

      // تحويل المصدر للاستعلام الثاني
      if (filters.source === 'sale' || filters.source === 'purchase' || filters.source === 'return') {
        transactionsConditions.push('t.transaction_type = ?');
        transactionsQueryParams.push(filters.source);
      }
    }
    // لا نضيف أي شروط إذا كان المصدر فارغًا، مما يعني عرض جميع المعاملات

    if (filters.startDate) {
      cashboxConditions.push('ct.created_at >= ?');
      cashboxQueryParams.push(filters.startDate);

      transactionsConditions.push('t.transaction_date >= ?');
      transactionsQueryParams.push(filters.startDate);
    }

    if (filters.endDate) {
      cashboxConditions.push('ct.created_at <= ?');
      cashboxQueryParams.push(filters.endDate);

      transactionsConditions.push('t.transaction_date <= ?');
      transactionsQueryParams.push(filters.endDate);
    }

    // إضافة شروط WHERE لاستعلام معاملات الخزينة
    if (cashboxConditions.length > 0) {
      cashboxQuery += ` WHERE ${cashboxConditions.join(' AND ')}`;
    }

    // إضافة شروط WHERE إضافية لاستعلام المعاملات
    if (transactionsConditions.length > 0) {
      transactionsQuery += ` AND ${transactionsConditions.join(' AND ')}`;
    }

    // إضافة الترتيب
    cashboxQuery += ' ORDER BY ct.created_at DESC';
    transactionsQuery += ' ORDER BY t.transaction_date DESC';

    // تنفيذ الاستعلامات
    console.log(`[CASHBOX-FIX] تنفيذ استعلام معاملات الخزينة: ${cashboxQuery}`);
    console.log(`[CASHBOX-FIX] معلمات استعلام الخزينة:`, cashboxQueryParams);

    console.log(`[CASHBOX-FIX] تنفيذ استعلام المعاملات: ${transactionsQuery}`);
    console.log(`[CASHBOX-FIX] معلمات استعلام المعاملات:`, transactionsQueryParams);

    const cashboxStmt = db.prepare(cashboxQuery);
    const transactionsStmt = db.prepare(transactionsQuery);

    const cashboxTransactions = cashboxStmt.all(...cashboxQueryParams);
    const regularTransactions = transactionsStmt.all(...transactionsQueryParams);

    console.log(`[CASHBOX-FIX] تم الحصول على ${cashboxTransactions.length} معاملة من جدول معاملات الخزينة`);
    console.log(`[CASHBOX-FIX] تم الحصول على ${regularTransactions.length} معاملة من جدول المعاملات العامة`);

    // تحويل معاملات البيع والشراء والاسترجاع إلى تنسيق متوافق مع معاملات الخزينة
    const formattedTransactions = regularTransactions.map(t => {
      // تعيين نوع المعاملة بشكل صحيح
      // نحتفظ بنوع المعاملة الأصلي (sale, purchase, return) في حقل transaction_type
      // ونستخدم نوع مناسب (income, expense, return) في حقل type للتوافق مع معاملات الخزينة
      let type;
      if (t.transaction_type === 'sale') {
        type = 'income'; // استخدام 'income' للتوافق مع معاملات الخزينة
      } else if (t.transaction_type === 'purchase') {
        type = 'expense'; // استخدام 'expense' للتوافق مع معاملات الخزينة
      } else if (t.transaction_type === 'return') {
        type = 'return';
      } else {
        type = t.transaction_type || t.type; // الحفاظ على النوع الأصلي إذا كان غير معروف
      }

      // إنشاء ملاحظات تتضمن الكمية فقط بدون اسم الصنف
      const notes = t.notes || `${t.transaction_type === 'sale' ? 'بيع' : t.transaction_type === 'purchase' ? 'شراء' : 'إرجاع'} ${t.quantity} صنف`;

      return {
        ...t,
        type,
        notes,
        // إضافة حقول إضافية للتمييز
        is_transaction: true,
        transaction_type: t.transaction_type,
        // الحفاظ على نوع المعاملة الأصلي في حقل source
        source: t.transaction_type
      };
    });

    // دمج المعاملات من كلا الجدولين
    const allTransactions = [...cashboxTransactions, ...formattedTransactions];

    // ترتيب المعاملات حسب التاريخ (الأحدث أولاً)
    allTransactions.sort((a, b) => {
      return new Date(b.created_at) - new Date(a.created_at);
    });

    // تجميع المعاملات حسب المصدر إذا تم طلب ذلك
    if (filters.groupBySource) {
      console.log('[CASHBOX-FIX] تجميع المعاملات حسب المصدر');

      // إضافة حقل source_group لكل معاملة
      allTransactions.forEach(transaction => {
        transaction.source_group = transaction.source || 'غير محدد';
      });
    }

    // تجميع المعاملات حسب التاريخ إذا تم طلب ذلك
    if (filters.groupByDate) {
      console.log('[CASHBOX-FIX] تجميع المعاملات حسب التاريخ');

      // إضافة حقل date_group لكل معاملة
      allTransactions.forEach(transaction => {
        const date = new Date(transaction.created_at);
        transaction.date_group = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
      });
    }

    console.log(`[CASHBOX-FIX] تم الحصول على إجمالي ${allTransactions.length} معاملة للخزينة`);

    // تسجيل أنواع المعاملات للتشخيص
    const transactionTypes = {};
    allTransactions.forEach(t => {
      const key = `${t.type}${t.transaction_source ? '-' + t.transaction_source : ''}`;
      transactionTypes[key] = (transactionTypes[key] || 0) + 1;
    });
    console.log(`[CASHBOX-FIX] أنواع المعاملات:`, transactionTypes);

    // تسجيل تفاصيل المعاملات للتشخيص
    console.log(`[CASHBOX-FIX] تفاصيل المعاملات:`);
    allTransactions.forEach((t, index) => {
      if (index < 10) { // عرض أول 10 معاملات فقط لتجنب الإفراط في التسجيل
        console.log(`[CASHBOX-FIX] معاملة ${index + 1}: النوع=${t.type}, المصدر=${t.source}, المبلغ=${t.amount}, مصدر المعاملة=${t.transaction_source || 'غير محدد'}`);
      }
    });

    return allTransactions;
  } catch (error) {
    console.error('[CASHBOX-FIX] خطأ في الحصول على معاملات الخزينة:', error);
    logError(error, 'getTransactions');
    return [];
  }
}

/**
 * حذف معاملة من الخزينة
 * @param {number} transactionId - معرف المعاملة
 * @returns {Object} - نتيجة العملية
 */
function deleteTransaction(transactionId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحويل إلى رقم
    const numericTransactionId = Number(transactionId);
    if (isNaN(numericTransactionId) || numericTransactionId <= 0) {
      return { success: false, error: `معرف المعاملة غير صالح: ${transactionId}` };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من وجود المعاملة
      const checkStmt = db.prepare('SELECT * FROM cashbox_transactions WHERE id = ?');
      const transaction = checkStmt.get(numericTransactionId);

      if (!transaction) {
        throw new Error(`المعاملة غير موجودة: ${numericTransactionId}`);
      }

      // التحقق من وجود الخزينة
      const checkCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const cashbox = checkCashboxStmt.get();

      if (!cashbox) {
        throw new Error('الخزينة غير موجودة');
      }

      // تحديث الخزينة
      const now = new Date().toISOString();

      // التحقق من نوع المعاملة لتحديد الحقل المناسب للتحديث
      if (transaction.source === 'transport') {
        // إذا كانت معاملة مصاريف نقل، نحدث transport_total
        const updateStmt = db.prepare(`
          UPDATE cashbox
          SET current_balance = current_balance + ?,
              transport_total = transport_total - ?,
              updated_at = ?
          WHERE id = ?
        `);

        updateStmt.run(
          transaction.amount,
          transaction.amount,
          now,
          cashbox.id
        );
      } else {
        // للمعاملات العادية
        const updateStmt = db.prepare(`
          UPDATE cashbox
          SET current_balance = current_balance ${transaction.type === 'income' ? '-' : '+'} ?,
              ${transaction.type === 'income' ? 'sales_total = sales_total - ?' : 'purchases_total = purchases_total - ?'},
              updated_at = ?
          WHERE id = ?
        `);

        updateStmt.run(
          transaction.amount,
          transaction.amount,
          now,
          cashbox.id
        );
      }

      // حذف المعاملة
      const deleteStmt = db.prepare('DELETE FROM cashbox_transactions WHERE id = ?');
      deleteStmt.run(numericTransactionId);

      // الحصول على الخزينة المحدثة
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const updatedCashbox = getCashboxStmt.get();

      // إرسال إشعار بتحديث الخزينة
      const cashboxData = {
        exists: true,
        id: updatedCashbox.id,
        initial_balance: updatedCashbox.initial_balance,
        current_balance: updatedCashbox.current_balance,
        profit_total: updatedCashbox.profit_total,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        transport_total: updatedCashbox.transport_total || 0,
        created_at: updatedCashbox.created_at,
        updated_at: updatedCashbox.updated_at
      };

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: updatedCashbox.id,
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        profit_total: updatedCashbox.profit_total,
        transaction_type: transaction.type === 'income' ? 'delete-income' : 'delete-expense',
        amount: transaction.amount,
        success: true
      });

      return {
        success: true,
        cashbox: cashboxData
      };
    })();
  } catch (error) {
    console.error('خطأ في حذف معاملة من الخزينة:', error);
    logError(error, 'deleteTransaction');
    return { success: false, error: error.message };
  }
}

/**
 * تحديث الخزينة بعد عملية إرجاع
 * @param {Object} returnData - بيانات عملية الإرجاع
 * @returns {Object} - نتيجة العملية
 */
function updateCashboxAfterReturn(returnData) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log(`[CASHBOX-RETURN] بدء تحديث الخزينة بعد عملية إرجاع:`, returnData);
    logSystem(`[CASHBOX-RETURN] بدء تحديث الخزينة بعد عملية إرجاع: ${JSON.stringify(returnData)}`, 'info');

    // التحقق من صحة البيانات
    if (!returnData.total_price || isNaN(Number(returnData.total_price))) {
      console.error(`[CASHBOX-RETURN] المبلغ غير صالح:`, returnData.total_price);
      return { success: false, error: `المبلغ غير صالح: ${returnData.total_price}` };
    }

    // التحويل إلى رقم
    const numericAmount = Number(returnData.total_price);
    if (numericAmount <= 0) {
      console.error(`[CASHBOX-RETURN] المبلغ يجب أن يكون أكبر من صفر:`, numericAmount);
      return { success: false, error: `المبلغ يجب أن يكون أكبر من صفر: ${numericAmount}` };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من وجود الخزينة
      const checkStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const cashbox = checkStmt.get();

      if (!cashbox) {
        console.error(`[CASHBOX-RETURN] الخزينة غير موجودة`);
        throw new Error('الخزينة غير موجودة');
      }

      console.log(`[CASHBOX-RETURN] معلومات الخزينة الحالية:`, cashbox);

      // إضافة معاملة للخزينة (مصروفات لأننا نعيد المال للعميل)
      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT INTO cashbox_transactions (
          type, amount, source, notes, user_id, created_at
        )
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      const result = insertStmt.run(
        'expense',
        numericAmount,
        'return',
        returnData.notes || `إرجاع للعميل ${returnData.customer || returnData.customer_id}`,
        returnData.user_id || null,
        now
      );

      console.log(`[CASHBOX-RETURN] تم إضافة معاملة للخزينة:`, result);

      // تحديث الخزينة (نخصم المبلغ من الرصيد الحالي، نخصم من إجمالي المبيعات، ونزيد إجمالي المرتجعات)
      const updateStmt = db.prepare(`
        UPDATE cashbox
        SET current_balance = current_balance - ?,
            sales_total = sales_total - ?,
            returns_total = returns_total + ?,
            updated_at = ?
        WHERE id = ?
      `);

      updateStmt.run(
        numericAmount,
        numericAmount,
        numericAmount,
        now,
        cashbox.id
      );

      console.log(`[CASHBOX-RETURN] تم تحديث الخزينة بنجاح`);

      // إعادة حساب الأرباح تلقائيًا بعد عملية الإرجاع
      // هذا يضمن أن أي مبلغ زائد عن الرصيد الافتتاحي سيتم نقله إلى الأرباح تلقائيًا
      console.log('[CASHBOX-RETURN] إعادة حساب الأرباح تلقائيًا بعد عملية الإرجاع');

      // استدعاء دالة fixProfitCalculation داخليًا (بدون استخدام API)
      const fixResult = fixProfitCalculationInternal();

      // الحصول على الخزينة المحدثة
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const updatedCashbox = getCashboxStmt.get();

      // تسجيل معلومات الخزينة المحدثة للتشخيص
      console.log(`[CASHBOX-RETURN] معلومات الخزينة بعد التحديث:`);
      console.log(`[CASHBOX-RETURN] - الرصيد الحالي: ${updatedCashbox.current_balance}`);
      console.log(`[CASHBOX-RETURN] - إجمالي المبيعات: ${updatedCashbox.sales_total}`);
      console.log(`[CASHBOX-RETURN] - إجمالي المشتريات: ${updatedCashbox.purchases_total}`);
      console.log(`[CASHBOX-RETURN] - إجمالي المرتجعات: ${updatedCashbox.returns_total}`);
      console.log(`[CASHBOX-RETURN] - إجمالي الأرباح: ${updatedCashbox.profit_total}`);

      // إرسال إشعار بتحديث الخزينة
      const cashboxData = {
        exists: true,
        id: updatedCashbox.id,
        initial_balance: updatedCashbox.initial_balance,
        current_balance: updatedCashbox.current_balance,
        profit_total: updatedCashbox.profit_total,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        created_at: updatedCashbox.created_at,
        updated_at: updatedCashbox.updated_at
      };

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: updatedCashbox.id,
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        profit_total: updatedCashbox.profit_total,
        transaction_type: 'return',
        amount: numericAmount,
        success: true
      });

      return {
        success: true,
        transaction_id: result.lastInsertRowid,
        cashbox: cashboxData
      };
    })();
  } catch (error) {
    console.error('[CASHBOX-RETURN] خطأ في تحديث الخزينة بعد عملية إرجاع:', error);
    logError(error, 'updateCashboxAfterReturn');
    return { success: false, error: error.message };
  }
}

/**
 * إصلاح حساب الأرباح في الخزينة (نسخة داخلية)
 *
 * هذه الدالة تعيد حساب إجمالي الأرباح بناءً على إجمالي المبيعات وإجمالي المشتريات
 * وتنقل أي مبلغ زائد من الرصيد الحالي إلى الأرباح
 *
 * @returns {Object} - نتيجة العملية
 */
function fixProfitCalculationInternal() {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // الحصول على الخزينة الحالية
    const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
    const cashbox = getCashboxStmt.get();

    if (!cashbox) {
      throw new Error('الخزينة غير موجودة');
    }

    // تسجيل قيم الخزينة الحالية
    console.log(`[PROFIT-FIX] قيم الخزينة قبل الإصلاح:`, {
      initial_balance: cashbox.initial_balance,
      current_balance: cashbox.current_balance,
      profit_total: cashbox.profit_total,
      sales_total: cashbox.sales_total,
      purchases_total: cashbox.purchases_total,
      returns_total: cashbox.returns_total || 0
    });

    // حساب المبلغ الزائد من الرصيد (إذا كان الرصيد الحالي أكبر من الرصيد الافتتاحي)
    let excessBalance = 0;
    let newCurrentBalance = cashbox.current_balance;

    if (cashbox.current_balance > cashbox.initial_balance) {
      excessBalance = cashbox.current_balance - cashbox.initial_balance;
      newCurrentBalance = cashbox.initial_balance;
      console.log(`[PROFIT-FIX] الرصيد الحالي (${cashbox.current_balance}) أكبر من الرصيد الافتتاحي (${cashbox.initial_balance})`);
      console.log(`[PROFIT-FIX] سيتم نقل ${excessBalance} من الرصيد الحالي إلى الأرباح`);
    }

    // استيراد وظائف حساب الربح
    const cashboxUtils = require('./utils/profitCalculator');

    // إعادة حساب إجمالي الأرباح بشكل صحيح
    const newProfit = cashboxUtils.recalculateTotalProfit(
      cashbox.sales_total,
      cashbox.purchases_total,
      excessBalance
    );

    console.log(`[PROFIT-FIX] الربح المحسوب: ${newProfit} (الربح القديم: ${cashbox.profit_total})`);

    // تحديث الخزينة بالقيم الجديدة
    const now = new Date().toISOString();
    const updateStmt = db.prepare(`
      UPDATE cashbox
      SET current_balance = ?,
          profit_total = ?,
          updated_at = ?
      WHERE id = ?
    `);

    updateStmt.run(
      newCurrentBalance,
      newProfit,
      now,
      cashbox.id
    );

    // الحصول على الخزينة المحدثة
    const updatedCashbox = getCashboxStmt.get();

    // تسجيل قيم الخزينة بعد الإصلاح
    console.log(`[PROFIT-FIX] قيم الخزينة بعد الإصلاح:`, {
      initial_balance: updatedCashbox.initial_balance,
      current_balance: updatedCashbox.current_balance,
      profit_total: updatedCashbox.profit_total,
      sales_total: updatedCashbox.sales_total,
      purchases_total: updatedCashbox.purchases_total,
      returns_total: updatedCashbox.returns_total || 0
    });

    return {
      success: true,
      cashbox: updatedCashbox
    };
  } catch (error) {
    console.error('خطأ في إصلاح حساب الأرباح (داخلي):', error);
    logError(error, 'fixProfitCalculationInternal');
    return { success: false, error: error.message };
  }
}

/**
 * إصلاح حساب الأرباح في الخزينة
 *
 * هذه الدالة تعيد حساب إجمالي الأرباح بناءً على إجمالي المبيعات وإجمالي المشتريات
 *
 * @returns {Object} - نتيجة العملية
 */
function fixProfitCalculation() {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // استدعاء الدالة الداخلية لإصلاح حساب الأرباح
      const result = fixProfitCalculationInternal();

      if (!result.success) {
        throw new Error(result.error || 'فشل في إصلاح حساب الأرباح');
      }

      const updatedCashbox = result.cashbox;

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: updatedCashbox.id,
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        transport_total: updatedCashbox.transport_total || 0,
        profit_total: updatedCashbox.profit_total,
        success: true
      });

      return {
        success: true,
        message: 'تم إصلاح حساب الأرباح بنجاح',
        cashbox: updatedCashbox
      };
    })();
  } catch (error) {
    console.error('خطأ في إصلاح حساب الأرباح:', error);
    logError(error, 'fixProfitCalculation');
    return { success: false, error: error.message };
  }
}

// تصدير الوظائف
module.exports = {
  initialize,
  getCashbox,
  createCashbox,
  updateInitialBalance,
  addTransaction,
  getTransactions,
  deleteTransaction,
  updateCashboxAfterReturn,
  fixProfitCalculation
};
